# ABOUTME: Test to verify that invitation notifications properly disappear after acceptance
# ABOUTME: and that styling is consistent in embedded mode within Mainbox
require 'rails_helper'

RSpec.describe 'Invitation notification persistence', type: :feature, js: true do
  let!(:company) { create(:company) }
  let(:password) { 'password123' }
  let!(:invitee_user) { create(:user, email: '<EMAIL>', password: password, confirmed_at: Time.current) }
  let!(:owner_role) { create(:role, name: 'owner') }
  let!(:employee_role) { create(:role, name: 'employee') }
  
  before do
    wait_for_vite_assets
    
    # Create a pending invitation (contract without user_id)
    create(:contract, 
           company: company, 
           email: invitee_user.email, 
           user_id: nil,
           job_title: 'Test Employee',
           first_name: 'Test',
           last_name: 'User')
  end

  scenario 'invitation notification disappears after acceptance in mainbox' do
    # Use JWT authentication
    authenticate_jwt_user(invitee_user)
    
    # Visit the main dashboard
    visit '/'
    wait_for_vite_assets
    
    # Verify invitation notification is visible in mainbox
    expect(page).to have_selector('.bg-yellow-100.border-l-4.border-yellow-500')
    expect(page).to have_text('NOVÉ')
    expect(page).to have_text(company.name)
    expect(page).to have_text('Test Employee')
    expect(page).to have_button('Připojit se k firmě')
    
    # Accept the invitation
    accept_confirm do
      click_button 'Připojit se k firmě'
    end
    
    # Wait for the API call to complete and UI to update
    sleep 1 # Allow time for the UI to update
    
    # Verify the notification box disappears completely
    expect(page).not_to have_selector('.bg-yellow-100.border-l-4.border-yellow-500', wait: 5)
    expect(page).not_to have_text('NOVÉ')
    expect(page).not_to have_button('Připojit se k firmě')
  end

  scenario 'embedded invitation has transparent styling (no white box)' do
    authenticate_jwt_user(invitee_user)
    visit '/'
    wait_for_vite_assets
    
    # Verify invitation is embedded without white background styling
    within('.bg-yellow-100.border-l-4.border-yellow-500') do
      # Should have company connections component
      expect(page).to have_selector('.connections-embedded')
      
      # Verify transparent styling is applied
      connections_list = find('.connections-list')
      
      # Check that the CSS classes are applied correctly
      expect(page).to have_css('.connections-embedded .connections-list')
    end
  end

  private

  def authenticate_jwt_user(user)
    # Create company user role for the user so they can access the system
    create(:company_user_role, user: user, company: company, role: employee_role, is_primary: true)
    
    # Use the JWT login endpoint to get a token
    post '/api/v1/auth/jwt_login', params: {
      email: user.email,
      password: password
    }
    
    expect(response).to have_http_status(:ok)
    token_data = JSON.parse(response.body)
    token = token_data['access_token']
    
    # Set the JWT token in browser localStorage
    page.driver.execute_script("localStorage.setItem('authToken', '#{token}')")
    
    # Also set a basic auth header for API requests
    page.driver.header('Authorization', "Bearer #{token}")
  end
end