<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {{ $t('accept_invitation', 'Complete Your Registration') }}
        </h2>
        <p v-if="invitationData" class="mt-2 text-center text-sm text-gray-600">
          {{ $t('invitation_for_company', 'You\'ve been invited to join {company}', { company: invitationData.company_name }) }}
        </p>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleAcceptInvitation">
        <!-- Success Message -->
        <div v-if="successMessage" class="rounded-md bg-green-50 p-4">
          <div class="flex">
            <div class="ml-3">
              <p class="text-sm font-medium text-green-800">
                {{ successMessage }}
              </p>
            </div>
          </div>
        </div>
        
        <!-- Error Messages -->
        <div v-if="errorMessages.length > 0" class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                {{ $t('invitation_failed', 'Invitation acceptance failed') }}
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc pl-5 space-y-1">
                  <li v-for="(error, index) in errorMessages" :key="index">
                    {{ error }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Invalid Token Warning -->
        <div v-if="!invitationToken || tokenInvalid" class="rounded-md bg-yellow-50 p-4">
          <div class="flex">
            <div class="ml-3">
              <p class="text-sm font-medium text-yellow-800">
                {{ $t('invalid_invitation_link', 'Invalid invitation link. Please request a new invitation.') }}
              </p>
            </div>
          </div>
        </div>
        
        <!-- Invitation Details -->
        <div v-if="invitationData && !tokenInvalid" class="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h3 class="text-sm font-medium text-blue-800">{{ $t('invitation_details', 'Invitation Details') }}</h3>
          <div class="mt-2 text-sm text-blue-700">
            <p><strong>{{ $t('company', 'Company') }}:</strong> {{ invitationData.company_name }}</p>
            <p><strong>{{ $t('invited_by', 'Invited by') }}:</strong> {{ invitationData.sender_name || invitationData.sender_email }}</p>
            <p v-if="invitationData.first_name || invitationData.last_name">
              <strong>{{ $t('invited_as', 'Invited as') }}:</strong> 
              {{ invitationData.first_name }} {{ invitationData.last_name }}
            </p>
          </div>
        </div>
        
        <!-- Registration Form -->
        <div v-if="invitationToken && !tokenInvalid" class="space-y-6">
          <div>
            <label for="password" class="sr-only">{{ $t('password', 'Password') }}</label>
            <input
              id="password"
              name="password"
              type="password"
              required
              v-model="form.password"
              class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              :placeholder="$t('password', 'Password')"
            />
          </div>
          
          <div>
            <label for="password-confirmation" class="sr-only">{{ $t('confirm_password', 'Confirm Password') }}</label>
            <input
              id="password-confirmation"
              name="password_confirmation"
              type="password"
              required
              v-model="form.password_confirmation"
              class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              :placeholder="$t('confirm_password', 'Confirm Password')"
            />
          </div>
          
          <div>
            <button
              type="submit"
              :disabled="loading || !invitationToken"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ loading ? $t('completing_registration', 'Completing registration...') : $t('complete_registration', 'Complete Registration') }}
            </button>
          </div>
        </div>
      </form>
      
      <!-- Back to Login Link -->
      <div class="text-center">
        <router-link 
          :to="{ name: 'login', params: { locale: $route.params.locale } }" 
          class="font-medium text-indigo-600 hover:text-indigo-500"
        >
          {{ $t('back_to_login', 'Back to login') }}
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'AcceptInvitationView',
  data() {
    return {
      invitationToken: null,
      invitationData: null,
      tokenInvalid: false,
      form: {
        password: '',
        password_confirmation: ''
      },
      loading: false,
      successMessage: '',
      errorMessages: []
    }
  },
  mounted() {
    // Get the invitation token from URL params
    this.invitationToken = this.$route.query.token
    
    if (this.invitationToken) {
      this.validateToken()
    }
  },
  methods: {
    async validateToken() {
      try {
        const response = await axios.get(`/api/v1/invitations/${this.invitationToken}`, {
          headers: { 'Accept': 'application/json' }
        })
        
        if (response.data.valid) {
          const invitation = response.data.invitation
          this.invitationData = {
            company_name: invitation.company.name,
            sender_email: invitation.sender.email,
            sender_name: `${invitation.sender.first_name || ''} ${invitation.sender.last_name || ''}`.trim(),
            first_name: invitation.first_name,
            last_name: invitation.last_name,
            invitation_type: invitation.type,
            already_connected: invitation.already_connected
          }
          
          // If user is already connected, show appropriate message
          if (invitation.already_connected) {
            this.errorMessages = [this.$t('already_connected_to_company', 'You are already connected to this company.')]
          }
          
          // If this is for an existing user, redirect to company invitation page
          if (invitation.type === 'existing_user') {
            this.$router.push({
              name: 'acceptCompanyInvitation',
              params: { locale: this.$route.params.locale },
              query: { token: this.invitationToken }
            })
          }
        } else {
          this.tokenInvalid = true
          this.errorMessages = [response.data.error || this.$t('invalid_invitation_token', 'Invalid invitation token')]
        }
      } catch (error) {
        console.error('Token validation error:', error)
        this.tokenInvalid = true
        if (error.response && error.response.data) {
          this.errorMessages = [error.response.data.error || this.$t('invalid_invitation_token', 'Invalid invitation token')]
        } else {
          this.errorMessages = [this.$t('network_error', 'Network error. Please try again.')]
        }
      }
    },
    
    async handleAcceptInvitation() {
      this.errorMessages = []
      this.successMessage = ''
      this.loading = true
      
      try {
        const response = await axios.post(`/api/v1/invitations/${this.invitationToken}/accept`, {
          password: this.form.password,
          password_confirmation: this.form.password_confirmation
        }, {
          headers: { 'Accept': 'application/json' }
        })
        
        if (response.data.success) {
          // Store the JWT token if provided
          if (response.data.access_token) {
            localStorage.setItem('tymbox_jwt_token', response.data.access_token)
            this.$store.commit('user/SET_JWT_TOKEN', response.data.access_token)
            this.$store.commit('user/setAuthenticated', true)
            
            if (response.data.user) {
              this.$store.commit('user/SET_JWT_USER', response.data.user)
            }
          }
          
          this.successMessage = response.data.message || this.$t('registration_successful', 'Registration completed successfully!')
          
          // SECURITY FIX (TYM-46): Redirect to company connections for manual acceptance
          // NEW users now follow same secure pattern as EXISTING users
          setTimeout(() => {
            this.$router.push({ name: 'companyConnections', params: { locale: this.$route.params.locale } })
          }, 2000)
        } else {
          this.errorMessages = [response.data.error || this.$t('registration_failed', 'Registration failed')]
        }
      } catch (error) {
        console.error('Invitation acceptance error:', error)
        
        if (error.response && error.response.data) {
          const errorData = error.response.data
          if (errorData.error) {
            this.errorMessages = [errorData.error]
          } else if (errorData.details) {
            this.errorMessages = Object.values(errorData.details).flat()
          } else {
            this.errorMessages = [this.$t('registration_failed', 'Registration failed')]
          }
        } else {
          this.errorMessages = [this.$t('network_error', 'Network error. Please try again.')]
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>