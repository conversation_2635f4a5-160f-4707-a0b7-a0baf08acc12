<template>
  <div class="connections" :class="{ 'connections-embedded': embedded }">
    <div class="section-header" v-if="!embedded">
      <h2>{{ $t('company_connections.title', '<PERSON>zvání ke spolupr<PERSON>ci') }}</h2>
    </div>
    
    <div class="connections-list">
      <template v-if="effectivePendingContracts.length">
        <div v-for="contract in effectivePendingContracts" 
             :key="contract.id" 
             class="connections-item">
          <div class="connections-item-content">
            <div class="connections-item-main">
              <div class="connections-item-head">
                {{ $t('company_connections.new', 'Nové pozvání') }}
              </div>
              <div class="connections-item-head">
                  {{ new Date(contract.created_at).toLocaleDateString('cs-CZ') }}
              </div>
            </div>
            <div class="connections-item-main">
              <div>
                <h3 class="connections-item-title">
                  {{ contract.company.name }}
                </h3>
                <p v-if="contract.job_title" class="connections-item-position">
                  {{ contract.job_title }}
                </p>
              </div>
            </div>

            <div class="connections-item-actions">
              <button @click="acceptConnection(contract)" 
                      class="btn btn-primary">
                {{ $t('company_connections.connect', 'Připojit se k firmě') }}
                <Check class="btn-icon" :size="16"/>
              </button>
            </div>
          </div>
        </div>
      </template>
      
      <div v-else-if="!embedded" class="connections-empty">
        <p class="text-muted">{{ $t('company_connections.no_invitations', 'Momentálně nemáte žádná pozvání.') }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { Check  } from 'lucide-vue-next'
import axios from 'axios'

export default {
  name: 'CompanyConnections',
  
  components: {
    Check
  },

  props: {
    embedded: {
      type: Boolean,
      default: false
    },
    pendingContracts: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      localPendingContracts: [],
      loading: false,
      error: null
    }
  },

  computed: {
    // Use prop data when embedded, otherwise use local data
    effectivePendingContracts() {
      return this.embedded ? this.pendingContracts : this.localPendingContracts;
    }
  },

  created() {
    // Only fetch data if not embedded (standalone mode)
    if (!this.embedded) {
      this.fetchPendingContracts()
    }
  },

  methods: {
    async fetchPendingContracts() {
      try {
        this.loading = true
        const response = await axios.get('/api/v1/company_connections/fetch')
        this.localPendingContracts = response.data
      } catch (err) {
        this.error = this.$t('company_connections.error', 'Nelze načíst pozvání')
        console.error('Error fetching connections:', err)
      } finally {
        this.loading = false
      }
    },

    async acceptConnection(contract) {
      if (!confirm(`${this.$t('company_connections.confirm_accept', 'Opravdu chcete přijmout pozvání od')} ${contract.company.name}?`)) {
        return
      }

      try {
        // JWT-only authentication mode - authorization handled automatically by axios interceptor
        await axios.post(`/api/v1/company_connections/${contract.id}/accept`)
        
        // Update local data based on mode
        if (this.embedded) {
          // In embedded mode, emit event to parent to handle data updates
          this.$emit('invitation-accepted', contract.id)
        } else {
          // In standalone mode, update local data
          this.localPendingContracts = this.localPendingContracts.filter(c => c.id !== contract.id)
        }
        
      } catch (err) {
        console.error('Error accepting connection:', err)
        alert(this.$t('company_connections.error_accepting', 'Nelze zpracovat pozvání. Zkuste to prosím později.'))
      }
    }
  }
}
</script>

<style>
.connections {
  max-width: 600px;
  margin: 0 auto;
}

.connections-list {
  background: white;
  border: 1px solid #eee;
  border-radius: 12px;
}

/* Embedded variant - remove white background and borders */
.connections-embedded .connections-list {
  background: transparent;
  border: none;
  border-radius: 0;
}

.connections-embedded .connections-item {
  border-bottom: none;
}

.connections-item {
  border-bottom: 1px solid #dee2e6;
}

.connections-item:last-child {
  border-bottom: none;
}

.connections-item-content {
  padding: 1rem;
}

.connections-item-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.connections-item-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.connections-item-head {
  display: flex;
  align-items: center;
  gap: 5px;
  padding-top: 4px;
  color: #666;
  font-size: 0.875rem;
}

.connections-item-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.connections-empty {
  padding: 1.5rem;
  text-align: center;
}

.btn-icon {
  margin-left: 0.5rem;
}

/* Embedded variant */
.connections-embedded .section-header {
  display: none;
}
</style>
