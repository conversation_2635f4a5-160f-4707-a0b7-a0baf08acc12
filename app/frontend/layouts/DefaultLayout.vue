<template>
  <div class="app-container" data-testid="authenticated-layout">
    <Sidebar 
      v-if="showSidebar" 
      :company-name="currentCompanyName"
      :current-plan-name="currentPlanName"
    />
    <div class="main-content">
      <Topbar 
        :company-name="currentCompanyName"
        :current-plan-name="currentPlanName"
        :user-role="userRole"
      />
      <div class="content-area">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import Topbar from '../components/Topbar.vue'
import Sidebar from '../components/Sidebar.vue'
import { mapState, mapGetters } from 'vuex'

export default {
  name: 'DefaultLayout',
  components: {
    Topbar,
    Sidebar
  },
  computed: {
    ...mapState(['sidebarVisible']),
    ...mapState('userStore', ['user']),
    ...mapGetters('userStore', ['currentCompany', 'currentRole']),
    showSidebar() {
      // Hide sidebar on mobile by default, show on desktop
      return this.sidebarVisible !== false
    },
    currentCompanyName() {
      return this.currentCompany?.name || ''
    },
    currentPlanName() {
      return this.currentCompany?.current_plan_name || 'free'
    },
    userRole() {
      return this.currentRole || ''
    }
  }
}
</script>

<style scoped>
/* Layout styles are handled by application.css */
</style>